# Testing Guide

This document provides information about how to run tests in this project, both locally and in CI.

## Overview

This project uses:

-   **Vitest** for unit testing
-   **Playwright** for end-to-end testing
-   **PostgreSQL** for the test database
-   **Drizzle ORM** for database access and seeding

## Test Structure

### Unit Tests

Unit tests are organized alongside the components they test:

```
frontend/components/
├── Button/
│   ├── Button.vue
│   └── Button.test.ts
├── Card/
│   ├── Card.vue
│   └── Card.test.ts
```

This co-location makes it easy to find tests and maintain them when components change.

### End-to-End Tests

End-to-end tests are located in the `frontend/tests/e2e` directory:

```
frontend/tests/
└── e2e/
    ├── index.spec.ts
    ├── auth.spec.ts
    └── ...
```

## End-to-End Tests

Our end-to-end tests use <PERSON><PERSON> to test the application in a real browser environment.

### Setting Up Local Environment for Tests

1. Copy the example environment file to create your local test environment:

    ```bash
    cp frontend/.env.test.local.example frontend/.env.test.local
    ```

2. Edit `.env.test.local` and add your actual credentials and API keys.

3. Start the test database:

    ```bash
    docker compose up db_test -d
    ```

4. Run the tests:
    ```bash
    cd frontend && npm run test:e2e
    ```

> **Note**: The test setup automatically checks if the database is available before running tests. If the database container isn't running, you'll see a clear error message instructing you to start it.

### GitHub Actions CI Setup

When tests run in GitHub Actions, they use secrets configured in the GitHub repository settings. You need to set up the following secrets:

1. Go to your GitHub repository
2. Navigate to Settings > Secrets and variables > Actions
3. Add the following repository secrets:
    - `SENTRY_DSN`: Your Sentry DSN for error tracking
    - `GOOGLE_CLIENT_ID`: Google OAuth client ID
    - `GOOGLE_CLIENT_SECRET`: Google OAuth client secret
    - `TWITTER_CLIENT_ID`: Twitter OAuth client ID
    - `TWITTER_CLIENT_SECRET`: Twitter OAuth client secret
    - `BETTER_AUTH_SECRET`: Secret for Better Auth
    - `RESEND_API_KEY`: API key for Resend email service

These secrets will be automatically used by the GitHub Actions workflow when running tests.

## Test Database

The test database setup is unified between local and CI environments:

### Local Development

-   A separate PostgreSQL container is used for tests (running on port 5433)
-   The database is automatically detected and connected to by the test setup
-   The database is seeded with test data when tests are run

### GitHub Actions CI

-   A PostgreSQL service container is created for each test run (on port 5432)
-   The database is automatically detected and connected to by the test setup
-   The database is seeded with test data when tests are run

## Environment Variables

The project uses a centralized approach to environment variables:

1. `.env.test` - Base test configuration (committed to git, no secrets)
2. `.env.test.local` - Local overrides with actual secrets (not committed to git)
3. GitHub Secrets - Used in CI environment

All environment variables are loaded through a centralized utility in `frontend/server/utils/env-loader.ts`, which:

-   Loads the base configuration from `.env.test`
-   Loads local overrides from `.env.test.local` if it exists and we're not in CI
-   Automatically adjusts the database connection string based on the environment

### E2E Test Environment

For E2E tests, we use:

-   **Production Build**: The application is built in production mode to match what users will experience
-   **Test Flag**: We set `NUXT_ENV_TEST=true` to indicate the test environment
-   **Test Database**: We connect to a separate test database
-   **Test Environment Variables**: We use `.env.test` and `.env.test.local` for configuration

This approach ensures that E2E tests run against a production-like build while still using test data and configuration.

## Test Structure

-   **Database Setup**: Handled by `db-setup.ts` which calls `db-seed.ts` to populate the database
-   **Environment Loading**: Centralized in `utils/env-loader.ts`
-   **Database Connection Check**: Automatically verifies the test database is available
-   **Test Execution**: Managed by Playwright using the configuration in `playwright.config.ts`

## Error Handling

The test setup includes improved error handling:

1. **Database Connection Errors**: If the test database isn't available, you'll see a clear error message with instructions to start the database container.
2. **Environment Variable Warnings**: If `.env.test.local` is missing, you'll get a warning about potentially missing configuration.
3. **Test Failures**: Playwright is configured to capture screenshots, videos, and traces on test failures to help with debugging.

## Running Different Types of Tests

-   **Unit Tests**: `npm run test`
-   **Unit Tests with Coverage**: `npm run test:coverage`
-   **Unit Tests with Coverage UI**: `npm run test:coverage:ui`
-   **E2E Tests**: `npm run test:e2e`
-   **E2E Tests with UI**: `npm run test:e2e:ui`
-   **E2E Tests in Headed Mode**: `npm run test:e2e:headed`
-   **E2E Tests in Debug Mode**: `npm run test:e2e:debug`

## Test Coverage

The project is configured to generate test coverage reports using Vitest's coverage tools. Coverage reports show how much of your code is covered by tests.

### Running Coverage Reports

```bash
# Generate coverage report
npm run test:coverage

# Generate coverage report with UI
npm run test:coverage:ui
```

### Coverage Configuration

Coverage is configured in `vitest.config.ts` with the following settings:

-   **Provider**: v8 (Node.js built-in coverage)
-   **Reporters**: text, JSON, and HTML
-   **Thresholds**:
    -   Lines: 70%
    -   Functions: 70%
    -   Branches: 60%
    -   Statements: 70%

### Coverage Exclusions

The following files and directories are excluded from coverage calculations:

-   **Test files**: `*.test.ts`, `*.spec.ts`, `tests/`
-   **Build and config files**: `node_modules/`, `dist/`, `.nuxt/`, `*.config.ts`, `nuxt.config.ts`, `playwright.config.ts`, `frontend/config/`
-   **Server and utility files**:
    -   `server/db/migrations/`
    -   `server/db/seed*.ts`
    -   `server/db/schema/`
    -   `server/plugins/`
    -   `server/utils/`
    -   `server/api/`
    -   `scripts/`
-   **Generated reports**: `playwright-report/`
-   **Auth schema**: `auth-schema.ts`
-   **Other non-testable files**: `*.d.ts`, `types/`, `mocks/`, `public/`, `assets/`, `lib/`

This ensures that coverage reports only include files that can and should be tested.

## Database Tools

-   **View Development Database**: `npm run db:studio`
-   **View Test Database**: `npm run db:studio:test`
    -   Note: The test database container must be running (`docker compose up db_test -d`)
-   **Generate Migrations**: `npm run db:generate`
-   **Apply Migrations to Development DB**: `npm run db:migrate`
-   **Apply Migrations to Test DB**: `npm run db:migrate:test`
    -   Note: This is usually not needed as migrations are applied automatically during tests
-   **Seed Development Database**: `npm run db:seed`
-   **Seed Test Database**: `npm run db:seed:test`
    -   Note: This will create test data in your test database for use with Drizzle Studio

## Schema Changes Workflow

When you make changes to your database schema:

1. Update schema files in `frontend/server/db/schema/`
2. Generate migrations: `npm run db:generate`
3. Apply migrations to development database: `npm run db:migrate`
4. Apply migrations to test database: `npm run db:migrate:test`
5. Start test database if not running: `docker compose up db_test -d`
6. Seed test database: `npm run db:seed:test`
7. View test database: `npm run db:studio:test`
8. Run tests to verify changes: `npm run test:e2e`

The migration scripts (`db:migrate` and `db:migrate:test`) use custom TypeScript scripts in the `frontend/scripts` directory that provide better error handling and environment management.

Note: When running tests with `npm run test:e2e`, migrations and seeding are handled automatically.
