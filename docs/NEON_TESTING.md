# Testing with Neon Database

This project uses a dedicated permanent Neon branch for testing. This document explains how to set up and use the Neon test branch.

## Setup

### 1. Create a Test Branch in Neon

1. Go to the Neon console
2. Navigate to the Branches tab
3. Click "Create Branch"
4. Name your branch "test"
5. Get the connection string for your test branch

### 2. Configure Environment Variables

Copy the example file and add your Neon test branch connection string:

```bash
cp frontend/.env.test.local.example frontend/.env.test.local
```

Then edit `.env.test.local` and update the `DATABASE_URL` with your Neon test branch connection string:

```
DATABASE_URL="postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"
```

## Running Tests

### Running Tests with Schema Updates

When you've made schema changes, run tests with schema updates:

```bash
npm run test:e2e:update-schema
```

This will:

1. Run migrations on your Neon test branch to update the schema
2. Run the tests against the test branch

### Running Tests Without Schema Updates

If you haven't made any schema changes, you can run tests directly:

```bash
npm run test:e2e
```

### Test Data

Since the test branch is created from production data, there's no need to seed it with test data. The test branch will always contain data from production, which provides a realistic testing environment.

If you need specific test data that doesn't exist in production, consider adding it directly through the Neon console.

## How It Works

The test setup uses the following components:

1. **Environment Variables**:

    - `.env.test` - Base test environment variables
    - `.env.test.local` - Local overrides with your Neon test branch connection string

2. **Database Setup**:

    - `tests/e2e/db-setup.ts` - Sets up the test database by running migrations and optionally seeding
    - `server/utils/env-loader.ts` - Loads environment variables for different environments

3. **Neon Integration**:
    - Uses `@neondatabase/serverless` package for connecting to Neon
    - Uses `drizzle-orm/neon-http` for ORM operations

## Benefits of Using a Persistent Test Branch

Using a persistent Neon test branch for testing has several benefits:

1. **Speed**: No need to recreate and seed the database for every test run
2. **Consistency**: Tests run against the same database state
3. **Debugging**: You can inspect the database state after tests run
4. **Isolation**: Test data is isolated from development and production data

## Troubleshooting

If you encounter issues with the test database:

1. **Connection Issues**:

    - Verify your connection string in `.env.test.local`
    - Make sure your Neon test branch is active

2. **Data Issues**:

    - If tests are failing due to data state, run `npm run test:e2e:seed-db` to reset the database

3. **Schema Issues**:
    - If you've made schema changes, make sure to run migrations on the test branch
