import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import PageNotificationCard from './PageNotificationCard.vue';

describe('PageNotificationCard Component', () => {
  it('renders properly with required props', () => {
    const wrapper = mount(PageNotificationCard, {
      props: {
        icon: 'test-icon',
        title: 'Test Title',
        message: 'Test message'
      },
      global: {
        stubs: {
          UIcon: true,
          UButton: true
        }
      }
    });
    
    expect(wrapper.find('h1').text()).toBe('Test Title');
    expect(wrapper.find('p').text()).toBe('Test message');
  });

  it('renders primary button when provided', () => {
    const wrapper = mount(PageNotificationCard, {
      props: {
        icon: 'test-icon',
        title: 'Test Title',
        message: 'Test message',
        primaryButton: {
          text: 'Primary Action',
          to: '/test'
        }
      },
      global: {
        stubs: {
          UIcon: true,
          UButton: true
        }
      }
    });
    
    const button = wrapper.findComponent({ name: 'UButton' });
    expect(button.exists()).toBe(true);
    expect(button.attributes('to')).toBe('/test');
    expect(button.text()).toContain('Primary Action');
  });

  it('emits primary-click event when primary button is clicked', async () => {
    const wrapper = mount(PageNotificationCard, {
      props: {
        icon: 'test-icon',
        title: 'Test Title',
        message: 'Test message',
        primaryButton: {
          text: 'Primary Action'
        }
      },
      global: {
        stubs: {
          UIcon: true
        }
      }
    });
    
    await wrapper.findComponent({ name: 'UButton' }).trigger('click');
    expect(wrapper.emitted('primary-click')).toBeTruthy();
  });

  it('renders custom content in slots', () => {
    const wrapper = mount(PageNotificationCard, {
      props: {
        icon: 'test-icon',
        title: 'Test Title',
        message: 'Test message'
      },
      slots: {
        alert: '<div class="test-alert">Alert content</div>',
        actions: '<button class="test-action">Custom Action</button>'
      },
      global: {
        stubs: {
          UIcon: true,
          UButton: true
        }
      }
    });
    
    expect(wrapper.find('.test-alert').exists()).toBe(true);
    expect(wrapper.find('.test-alert').text()).toBe('Alert content');
    expect(wrapper.find('.test-action').exists()).toBe(true);
  });
});