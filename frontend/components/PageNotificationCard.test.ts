import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import PageNotificationCard from './PageNotificationCard.vue';

describe('PageNotificationCard Component', () => {
	it('renders properly with required props', () => {
		const wrapper = mount(PageNotificationCard, {
			props: {
				icon: 'test-icon',
				title: 'Test Title',
				message: 'Test message',
			},
			global: {
				stubs: {
					UIcon: true,
				},
			},
		});

		expect(wrapper.find('h1').text()).toBe('Test Title');
		expect(wrapper.find('p').text()).toBe('Test message');
	});

	it('renders with custom icon class', () => {
		const wrapper = mount(PageNotificationCard, {
			props: {
				icon: 'test-icon',
				iconClass: 'custom-icon-class',
				title: 'Test Title',
				message: 'Test message',
			},
			global: {
				stubs: {
					UIcon: true,
				},
			},
		});

		const icon = wrapper.findComponent({ name: 'UIcon' });
		expect(icon.attributes('class')).toContain('custom-icon-class');
	});

	it('renders custom content in slots', () => {
		const wrapper = mount(PageNotificationCard, {
			props: {
				icon: 'test-icon',
				title: 'Test Title',
				message: 'Test message',
			},
			slots: {
				alert: '<div class="test-alert">Alert content</div>',
				actions: '<button class="test-action">Custom Action</button>',
			},
			global: {
				stubs: {
					UIcon: true,
				},
			},
		});

		expect(wrapper.find('.test-alert').exists()).toBe(true);
		expect(wrapper.find('.test-alert').text()).toBe('Alert content');
		expect(wrapper.find('.test-action').exists()).toBe(true);
	});
});
