<template>
	<NuxtLayout name="auth">
		<div class="space-y-6 sm:space-y-10 -mt-4 md:mt-0">
			<PageNotificationCard
				:icon="cardConfig.icon"
				:iconClass="cardConfig.iconClass"
				:title="cardConfig.title"
				:message="cardConfig.message">
				<template
					#alert
					v-if="resendError">
					<UAlert
						color="error"
						variant="soft"
						:title="resendError"
						:close-button="{ icon: 'i-lucide-x', color: 'gray', variant: 'link', padded: false }"
						@close="resendError = null" />
				</template>
				<template #actions>
					<UButton
						block
						size="xl"
						class="text-sm py-3"
						v-bind="cardConfig.button"
						@click="handleButtonClick">
						{{ cardConfig.button.label }}
					</UButton>
				</template>
			</PageNotificationCard>
		</div>
	</NuxtLayout>
</template>

<script lang="ts" setup>
import { ref, watch, computed } from 'vue';
import { authClient, useAuthSession } from '~/server/utils/auth-client';
import type { ButtonProps } from '#ui/types';

definePageMeta({
	title: 'Verify Account',
	layout: false,
});

// Types
type Mode = 'verified' | 'resend' | 'logIn';

// Use Nuxt UI's Button props directly and extend with our custom action property
type ButtonConfig = ButtonProps & {
	action?: string;
};

// Session and route
const { data: session } = await useAuthSession(useFetch);
const route = useRoute();
const loading = ref<boolean>(false);
const sent = ref<boolean>(false); // Track whether the email has been sent

// Computed error state with proper typing
const hasError = computed(() => {
	const errorParam = route.query.error;
	return typeof errorParam === 'string' && ['invalid_token', 'expired_token', 'already_verified'].includes(errorParam);
});

// Writable mode
const mode = ref<Mode>('logIn');

// Initialize mode
const initializeMode = () => {
	if (session.value) {
		mode.value = session.value.user.emailVerified ? 'verified' : 'resend';
	} else {
		mode.value = 'logIn';
	}
};
initializeMode();

// Watch for changes in route or session state
watch(() => [route.query.error, session.value?.user.emailVerified], initializeMode);

// Error state for user feedback
const resendError = ref<string | null>(null);

// Combined configuration for card and button
const cardConfig = computed(() => {
	// Default configuration
	let config: {
		icon: string;
		iconClass: string;
		title: string;
		message: string;
		button: ButtonConfig;
	} = {
		// Card properties
		icon: '',
		iconClass: 'size-12',
		title: '',
		message: '',

		// Button properties
		button: {
			label: '',
			color: 'primary',
			variant: 'solid',
		},
	};

	// Configure based on mode
	if (mode.value === 'verified') {
		config = {
			...config,
			icon: 'i-lucide-circle-check-big',
			iconClass: 'size-12 text-(--ui-success)',
			title: 'Email Verified',
			message: 'Your email was successfully verified. Please click below to continue.',
			button: {
				...config.button,
				label: 'Continue',
				to: '/',
			},
		};
	} else if (mode.value === 'resend' && !hasError.value) {
		config = {
			...config,
			icon: sent.value ? 'i-lucide-circle-check-big' : 'i-lucide-mail-check',
			iconClass: sent.value ? 'size-12 text-(--ui-success)' : 'size-12 text-(--ui-neutral)',
			title: sent.value ? 'Verification Sent' : 'Please Verify Your Account',
			message: sent.value
				? 'We have sent the verification email. Check your inbox.'
				: 'Please click the link we sent to you when you signed up or click below to resend.',
			button: {
				...config.button,
				label: sent.value ? 'Verification Sent' : 'Resend Verification Email',
				color: sent.value ? 'neutral' : 'primary',
				variant: sent.value ? 'soft' : 'solid',
				disabled: sent.value,
				loading: loading.value,
				action: sent.value ? undefined : 'resend',
			},
		};
	} else if (mode.value === 'resend' && hasError.value) {
		config = {
			...config,
			icon: 'i-lucide-frown',
			iconClass: 'size-12 text-(--ui-error)',
			title: 'There Was A Problem',
			message: 'The verification link is invalid or expired. Please click below to resend the verification email.',
			button: {
				...config.button,
				label: 'Resend Verification Email',
				loading: loading.value,
				action: 'resend',
			},
		};
	} else if (mode.value === 'logIn' && hasError.value) {
		config = {
			...config,
			icon: 'i-lucide-frown',
			iconClass: 'size-12 text-(--ui-error)',
			title: 'There Was A Problem',
			message: 'The verification link is invalid or expired. Please log in to resend the verification email.',
			button: {
				...config.button,
				label: 'Log In',
				to: '/sign-in',
			},
		};
	} else if (mode.value === 'logIn' && !hasError.value) {
		config = {
			...config,
			icon: 'i-lucide-log-in',
			iconClass: 'size-12 text-(--ui-muted)',
			title: 'Please Log In',
			message: 'Log in to access your account and verify your email.',
			button: {
				...config.button,
				label: 'Log In',
				to: '/sign-in',
			},
		};
	}

	return config;
});

// Handle button click based on current mode
const handleButtonClick = async () => {
	if (cardConfig.value.button.action === 'resend') {
		await handleResend();
	}
};

// Handle resend email action
const handleResend = async () => {
	if (!session.value?.user.email) return;

	loading.value = true;
	resendError.value = null; // Clear previous errors

	try {
		await authClient.sendVerificationEmail({
			email: session.value.user.email,
			callbackURL: '/verify', // The redirect URL after verification
		});
		sent.value = true; // Mark as sent
	} catch (error) {
		console.error('Failed to resend verification email:', error);

		// Provide user-friendly error messages
		if (error instanceof Error) {
			resendError.value = error.message.includes('rate limit')
				? 'Please wait before requesting another verification email.'
				: 'Failed to send verification email. Please try again.';
		} else {
			resendError.value = 'An unexpected error occurred. Please try again.';
		}
	} finally {
		loading.value = false; // Reset loading state
	}
};
</script>
