<template>
	<NuxtLayout name="auth">
		<div class="space-y-6 sm:space-y-10 -mt-4 md:mt-0">
			<!-- Not signed in - show login prompt -->
			<PageNotificationCard
				v-if="!session"
				:icon="hasError ? 'i-lucide-frown' : 'i-lucide-log-in'"
				:iconClass="hasError ? 'size-12 text-(--ui-error)' : 'size-12 text-(--ui-muted)'"
				:title="hasError ? 'There Was A Problem' : 'Please Log In'"
				:message="
					hasError
						? 'The verification link is invalid or expired. Please log in to resend the verification email.'
						: 'Log in to access your account and verify your email.'
				">
				<template #actions>
					<UButton
						block
						size="xl"
						class="text-sm py-3"
						color="primary"
						to="/sign-in">
						Log In
					</UButton>
				</template>
			</PageNotificationCard>

			<!-- Signed in with verified email - show success -->
			<PageNotificationCard
				v-else-if="session?.user?.emailVerified"
				icon="i-lucide-circle-check-big"
				iconClass="size-12 text-(--ui-success)"
				title="Email Verified"
				message="Your email was successfully verified. Please click below to continue.">
				<template #actions>
					<UButton
						block
						size="xl"
						class="text-sm py-3"
						color="primary"
						to="/">
						Continue
					</UButton>
				</template>
			</PageNotificationCard>

			<!-- Signed in with unverified email - show resend option -->
			<PageNotificationCard
				v-else-if="session"
				:icon="hasError ? 'i-lucide-frown' : sent ? 'i-lucide-circle-check-big' : 'i-lucide-mail-check'"
				:iconClass="hasError ? 'size-12 text-(--ui-error)' : sent ? 'size-12 text-(--ui-success)' : 'size-12 text-(--ui-neutral)'"
				:title="hasError ? 'There Was A Problem' : sent ? 'Verification Sent' : 'Please Verify Your Account'"
				:message="
					hasError
						? 'The verification link is invalid or expired. Please click below to resend the verification email.'
						: sent
						? 'We have sent the verification email. Check your inbox.'
						: 'Please click the link we sent to you when you signed up or click below to resend.'
				">
				<template
					#alert
					v-if="resendError">
					<UAlert
						color="error"
						variant="soft"
						:title="resendError"
						:close-button="{ icon: 'i-lucide-x', color: 'gray', variant: 'link', padded: false }"
						@close="resendError = null" />
				</template>
				<template #actions>
					<UButton
						block
						size="xl"
						class="text-sm py-3"
						:color="sent ? 'neutral' : 'primary'"
						:variant="sent ? 'soft' : 'solid'"
						:disabled="sent"
						:loading="loading"
						@click="handleResend">
						{{ sent ? 'Verification Sent' : 'Resend Verification Email' }}
					</UButton>
				</template>
			</PageNotificationCard>
		</div>
	</NuxtLayout>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { authClient, useAuthSession } from '~/server/utils/auth-client';

definePageMeta({
	title: 'Verify Account',
	layout: false,
});

// Session and route
const { data: session } = await useAuthSession(useFetch);
const route = useRoute();
const loading = ref<boolean>(false);
const sent = ref<boolean>(false); // Track whether the email has been sent

// Computed error state with proper typing
const hasError = computed(() => {
	const errorParam = route.query.error;
	return typeof errorParam === 'string' && ['invalid_token', 'expired_token', 'already_verified'].includes(errorParam);
});

// Error state for user feedback
const resendError = ref<string | null>(null);

// Handle resend email action
const handleResend = async () => {
	if (!session.value?.user.email) return;

	loading.value = true;
	resendError.value = null; // Clear previous errors

	try {
		await authClient.sendVerificationEmail({
			email: session.value.user.email,
			callbackURL: '/verify', // The redirect URL after verification
		});
		sent.value = true; // Mark as sent
	} catch (error) {
		console.error('Failed to resend verification email:', error);

		// Provide user-friendly error messages
		if (error instanceof Error) {
			resendError.value = error.message.includes('rate limit')
				? 'Please wait before requesting another verification email.'
				: 'Failed to send verification email. Please try again.';
		} else {
			resendError.value = 'An unexpected error occurred. Please try again.';
		}
	} finally {
		loading.value = false; // Reset loading state
	}
};
</script>
