<script lang="ts" setup>
import { useAuthSession, signOut } from '~/server/utils/auth-client';
import { useRouter, useRoute } from 'vue-router';

definePageMeta({
	pageId: 'index',
});

const { data: session } = await useAuthSession(useFetch);

const router = useRouter();
// --- Functions ---
const handleSignOut = async () => {
	await signOut();
	router.push('/');
};
</script>

<template>
	<div v-if="session?.user">
		<h1>Hello {{ session.user.name }}</h1>
		<p v-if="session.user.emailVerified">{{ session.user.email }} (verified)</p>
		<p v-else>{{ session.user.email }} (not verified)</p>
		<UButton @click="handleSignOut">Sign out</UButton>
	</div>
	<div v-else>
		<h1>Please <NuxtLink to="/sign-in">sign in</NuxtLink></h1>
	</div>

	<div class="border border-dimmed p-4 mt-4">This div uses the border-dimmed utility class</div>
</template>
