/**
 * Database setup for E2E tests
 *
 * This file handles database schema updates and migrations for the test environment.
 * It ensures the test database is in the correct state before running tests.
 */

import { execSync } from 'child_process';

export async function setupTestDatabase() {
	console.log('Updating test database schema...');

	try {
		// Run database migrations on the test database
		console.log('Running migrations on test database...');
		execSync('npm run db:migrate', {
			stdio: 'inherit',
			env: process.env,
		});
		console.log('Migrations completed successfully');

		// Note: We don't seed the database as data will accumulate naturally from app usage
		console.log('Using existing data from permanent Neon test branch');
	} catch (error) {
		console.error('Database setup failed:', error);
		throw error;
	}
}
