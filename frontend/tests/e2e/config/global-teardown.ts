/**
 * Global teardown for E2E tests
 *
 * This file runs once after all tests complete. It handles cleanup of any
 * global resources that were set up during the test run.
 */

async function globalTeardown() {
	console.log('Global teardown - cleaning up after tests');
	
	try {
		// Note: We don't clean up the test database as we're using a permanent Neon test branch
		// Individual tests handle their own user cleanup via deleteTestUser()
		
		console.log('Global teardown completed');
	} catch (error) {
		console.error('Global teardown failed:', error);
		// Don't throw here as we don't want to fail the test run during cleanup
	}
}

export default globalTeardown;
