/**
 * Global setup for E2E tests
 *
 * This file runs once before all tests start. It sets up the test environment,
 * including database preparation and any other global configuration needed.
 */

import { setupTestDatabase } from './db-setup.js';

async function globalSetup() {
	console.log('Starting global setup for E2E tests...');
	
	try {
		// Set up the test database
		await setupTestDatabase();
		
		console.log('Global setup completed successfully');
	} catch (error) {
		console.error('Global setup failed:', error);
		throw error;
	}
}

export default globalSetup;
