/**
 * E2E test helpers
 *
 * This file contains helper functions for E2E tests to make them more maintainable
 * and consistent across the test suite.
 */
import type { Page } from '@playwright/test';

/**
 * Waits for the page to be fully loaded and hydrated
 * @param page Playwright page object
 */
export async function waitForPageLoad(page: Page): Promise<void> {
	// Wait for Nuxt hydration to complete
	await page.waitForFunction(
		() => {
			// @ts-ignore - __NUXT__ is added by Nuxt.js
			return window.__NUXT__ !== undefined;
		},
		{ timeout: process.env.CI ? 60000 : 30000 }
	);
}

/**
 * Signs in a user via the sign-in form
 * @param page Playwright page object
 * @param email User email
 * @param password User password
 */
export async function signInUser(page: Page, email: string, password: string): Promise<void> {
	// Navigate to sign-in page
	await page.goto('/sign-in');
	await waitForPageLoad(page);

	// Fill in sign-in form
	await page.getByPlaceholder('Email address').fill(email);
	await page.getByPlaceholder('Password').fill(password);

	// Submit form
	await page.getByRole('button', { name: 'Sign In' }).click();

	// Wait for navigation away from sign-in page
	await page.waitForURL((url) => !url.pathname.includes('/sign-in'));
}
