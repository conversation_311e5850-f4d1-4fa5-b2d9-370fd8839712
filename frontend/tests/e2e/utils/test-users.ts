/**
 * Test user management utilities
 *
 * This file provides utilities for managing test users in e2e tests.
 * Users are created programmatically via Better Auth API to ensure proper integration.
 */

import { db } from '../../../server/utils/drizzle.js';
import { user } from '../../../server/db/schema/auth.js';
import { eq } from 'drizzle-orm';
import { auth } from '../../../server/utils/auth.js';

/**
 * Interface for test user data
 */
export interface TestUserData {
	name: string;
	email: string;
	password: string;
}

/**
 * Generates test user data with unique email
 * @param overrides Optional overrides for default values
 * @returns TestUserData object
 */
export function generateTestUserData(overrides: Partial<TestUserData> = {}): TestUserData {
	const timestamp = Date.now();
	return {
		name: 'Test User',
		email: `test-user-${timestamp}@example.com`,
		password: 'TestPassword123!',
		...overrides,
	};
}

/**
 * Creates a test user via Better Auth API
 * @param userData User data to create
 * @returns Promise that resolves when user is created
 */
export async function createTestUser(userData: TestUserData): Promise<void> {
	await auth.api.signUpEmail({
		body: {
			name: userData.name,
			email: userData.email,
			password: userData.password,
		},
	});
}

/**
 * Creates a verified test user
 * @param userData User data to create
 * @returns Promise that resolves when verified user is created
 */
export async function createVerifiedTestUser(userData: TestUserData): Promise<void> {
	// Create the user first
	await createTestUser(userData);

	// Mark as verified by updating the database directly
	// This is a test utility, so direct DB manipulation is acceptable
	await db.update(user).set({ emailVerified: true }).where(eq(user.email, userData.email));
}

/**
 * Deletes a test user from the database
 * @param email Email of the user to delete
 */
export async function deleteTestUser(email: string): Promise<void> {
	try {
		await db.delete(user).where(eq(user.email, email));
	} catch (error) {
		// Don't throw here as the user might not exist
	}
}
