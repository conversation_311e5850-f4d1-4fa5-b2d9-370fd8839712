/**
 * Email template utilities for Better Auth integration
 */

interface EmailTemplateData {
	userName?: string;
	userEmail: string;
	url: string;
	appName?: string;
}

/**
 * Base email template with consistent styling
 */
function createBaseTemplate(content: string, appName = 'Foundation'): string {
	return `
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>${appName}</title>
	<style>
		body {
			font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
			line-height: 1.6;
			color: #333;
			max-width: 600px;
			margin: 0 auto;
			padding: 20px;
			background-color: #f9f9f9;
		}
		.container {
			background-color: white;
			padding: 40px;
			border-radius: 8px;
			box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
		}
		.header {
			text-align: center;
			margin-bottom: 30px;
		}
		.logo {
			font-size: 24px;
			font-weight: 800;
			color: #2563eb;
			margin-bottom: 10px;
		}
		.button {
			display: inline-block;
			padding: 12px 24px;
			background-color: #2563eb;
			color: white;
			text-decoration: none;
			border-radius: 6px;
			font-weight: 500;
			margin: 20px 0;
		}
		.footer {
			margin-top: 30px;
			padding-top: 20px;
			border-top: 1px solid #eee;
			font-size: 14px;
			color: #666;
		}
		.url-fallback {
			word-break: break-all;
			color: #666;
			font-size: 14px;
			margin-top: 10px;
		}
	</style>
</head>
<body>
	<div class="container">
		<div class="header">
			<div class="logo">${appName}</div>
		</div>
		${content}
		<div class="footer">
			<p>If you didn't request this email, you can safely ignore it.</p>
			<p>This email was sent from ${appName}.</p>
		</div>
	</div>
</body>
</html>
	`.trim();
}

/**
 * Email verification template
 */
export function createVerificationEmailTemplate(data: EmailTemplateData): string {
	const { userName, userEmail, url, appName } = data;
	const displayName = userName || userEmail;

	const content = `
		<h2>Verify your email address</h2>
		<p>Hello ${displayName},</p>
		<p>Thank you for signing up! Please verify your email address to complete your account setup.</p>
		<p style="text-align: center;">
			<a href="${url}" class="button">Verify Email Address</a>
		</p>
		<p>Or copy and paste this link into your browser:</p>
		<p class="url-fallback">${url}</p>
	`;

	return createBaseTemplate(content, appName);
}

/**
 * Password reset template
 */
export function createPasswordResetTemplate(data: EmailTemplateData): string {
	const { userName, userEmail, url, appName } = data;
	const displayName = userName || userEmail;

	const content = `
		<h2>Reset your password</h2>
		<p>Hello ${displayName},</p>
		<p>We received a request to reset your password. Click the button below to create a new password:</p>
		<p style="text-align: center;">
			<a href="${url}" class="button">Reset Password</a>
		</p>
		<p>Or copy and paste this link into your browser:</p>
		<p class="url-fallback">${url}</p>
		<p><strong>This link will expire in 1 hour for security reasons.</strong></p>
	`;

	return createBaseTemplate(content, appName);
}
