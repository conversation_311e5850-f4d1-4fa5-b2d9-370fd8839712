/**
 * Database migrations utility
 *
 * This module provides functions for running database migrations using Drizzle ORM with Neon.
 * It can be used for both development and test databases.
 */

import { drizzle } from 'drizzle-orm/neon-http';
import { migrate } from 'drizzle-orm/neon-http/migrator';
import { neon } from '@neondatabase/serverless';
import { loadEnv, type Environment } from './env-loader.js';

/**
 * Environment type for migrations
 */
export type MigrationEnvironment = Environment;

/**
 * Options for running migrations
 */
export interface MigrationOptions {
	/** The environment to run migrations for */
	environment?: MigrationEnvironment;
	/** The database URL to use (overrides environment-based URL) */
	databaseUrl?: string;
	/** The folder containing migration files */
	migrationsFolder?: string;
	/** The table to store migration metadata */
	migrationsTable?: string;
}

/**
 * Run database migrations
 * @param options Migration options
 * @returns A promise that resolves when migrations are complete
 */
export async function runMigrations(options: MigrationOptions = {}): Promise<void> {
	// Set default options
	const environment = options.environment || 'development';
	const migrationsFolder = options.migrationsFolder || './server/db/migrations';
	const migrationsTable = options.migrationsTable || 'drizzle_migrations';

	// Load environment variables if no database URL is provided
	if (!options.databaseUrl) {
		loadEnv(environment);
	}

	// Get database URL from options or environment variables
	const databaseUrl = options.databaseUrl || process.env.DATABASE_URL;

	if (!databaseUrl) {
		throw new Error(`DATABASE_URL not found for ${environment} environment`);
	}

	console.log(`Running migrations on ${environment} database...`);

	// Create Drizzle ORM instance with Neon
	const db = drizzle(neon(databaseUrl));

	// Run migrations
	await migrate(db, {
		migrationsFolder,
		migrationsTable,
	});

	console.log('Migrations completed successfully');
}

/**
 * Command-line migration script
 *
 * This function is called when this file is executed directly.
 * It parses command-line arguments and runs migrations accordingly.
 */
export async function runMigrationsFromCommandLine(): Promise<void> {
	try {
		// Parse command line arguments
		const isTest = process.argv.includes('--test');
		const environment: MigrationEnvironment = isTest ? 'test' : 'development';

		// Run migrations
		await runMigrations({ environment });
	} catch (error) {
		console.error('Error running migrations:', error);
		process.exit(1);
	}
}

// Run migrations if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
	runMigrationsFromCommandLine();
}
